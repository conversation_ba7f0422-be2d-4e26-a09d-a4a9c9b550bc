//
//  DataBackupManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit
import Combine

/**
 * 数据备份恢复管理器
 * 专门负责关键数据的备份和恢复，特别是积分记录等重要数据
 * 确保在应用卸载重装后能够恢复用户的重要数据
 */
@MainActor
class DataBackupManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataBackupManager()
    
    // MARK: - Published Properties
    @Published var isRestoring: Bool = false
    @Published var restoreProgress: Double = 0.0
    @Published var restoreStatus: RestoreStatus = .idle
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let dataManager = DataManager.shared
    private let cloudKitContainer = CKContainer.default()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Constants
    private let maxRetryAttempts = 3
    private let retryDelay: TimeInterval = 2.0
    
    // MARK: - Initialization
    private init() {
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查并恢复丢失的数据
     */
    func checkAndRestoreLostData() async {
        print("🔍 开始检查丢失的数据...")
        
        isRestoring = true
        restoreStatus = .checking
        restoreProgress = 0.1
        
        do {
            // 1. 检查CloudKit可用性
            let isAvailable = await checkCloudKitAvailability()
            guard isAvailable else {
                throw BackupError.cloudKitUnavailable
            }
            restoreProgress = 0.2
            
            // 2. 检查本地数据完整性
            let missingData = await identifyMissingData()
            restoreProgress = 0.4
            
            if missingData.isEmpty {
                print("✅ 数据完整，无需恢复")
                restoreStatus = .completed
                restoreProgress = 1.0
                isRestoring = false
                return
            }
            
            // 3. 从CloudKit恢复数据
            print("⚠️ 检测到缺失数据: \(missingData)")
            await restoreDataFromCloudKit(missingData)
            restoreProgress = 0.8
            
            // 4. 验证恢复结果
            let verificationResult = await verifyRestoredData()
            restoreProgress = 1.0
            
            if verificationResult {
                restoreStatus = .completed
                print("✅ 数据恢复完成")
            } else {
                restoreStatus = .failed
                print("❌ 数据恢复验证失败")
            }
            
        } catch {
            restoreStatus = .failed
            errorMessage = error.localizedDescription
            print("❌ 数据恢复失败: \(error)")
        }
        
        isRestoring = false
    }
    
    /**
     * 强制从CloudKit恢复所有数据
     */
    func forceRestoreFromCloudKit() async -> Bool {
        print("🔄 强制从CloudKit恢复所有数据...")
        
        isRestoring = true
        restoreStatus = .restoring
        
        let allDataTypes: [MissingDataType] = [.pointRecords, .members, .diaryEntries, .redemptionRecords, .lotteryRecords]
        
        let success = await restoreDataFromCloudKit(allDataTypes)
        
        isRestoring = false
        restoreStatus = success ? .completed : .failed
        
        return success
    }
    
    /**
     * 创建数据快照备份
     */
    func createDataSnapshot() async -> Bool {
        print("📸 创建数据快照备份...")
        
        do {
            let snapshot = await collectDataSnapshot()
            let success = await uploadSnapshotToCloudKit(snapshot)
            
            if success {
                print("✅ 数据快照备份完成")
                return true
            } else {
                print("❌ 数据快照备份失败")
                return false
            }
            
        } catch {
            print("❌ 创建数据快照失败: \(error)")
            return false
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听应用进入前台，检查数据完整性
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.checkAndRestoreLostData()
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 检查CloudKit可用性
     */
    private func checkCloudKitAvailability() async -> Bool {
        do {
            let status = try await cloudKitContainer.accountStatus()
            return status == .available
        } catch {
            print("❌ CloudKit可用性检查失败: \(error)")
            return false
        }
    }
    
    /**
     * 识别缺失的数据
     */
    private func identifyMissingData() async -> [MissingDataType] {
        let context = dataManager.viewContext
        var missingData: [MissingDataType] = []
        
        await context.perform {
            // 检查积分记录
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            let pointRecordCount = (try? context.count(for: pointRecordRequest)) ?? 0
            if pointRecordCount == 0 {
                missingData.append(.pointRecords)
            }
            
            // 检查成员数据
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            let memberCount = (try? context.count(for: memberRequest)) ?? 0
            if memberCount == 0 {
                missingData.append(.members)
            }
            
            // 检查日记条目
            let diaryRequest: NSFetchRequest<DiaryEntry> = DiaryEntry.fetchRequest()
            let diaryCount = (try? context.count(for: diaryRequest)) ?? 0
            if diaryCount == 0 {
                missingData.append(.diaryEntries)
            }
            
            // 检查兑换记录
            let redemptionRequest: NSFetchRequest<RedemptionRecord> = RedemptionRecord.fetchRequest()
            let redemptionCount = (try? context.count(for: redemptionRequest)) ?? 0
            if redemptionCount == 0 {
                missingData.append(.redemptionRecords)
            }
            
            // 检查抽奖记录
            let lotteryRequest: NSFetchRequest<LotteryRecord> = LotteryRecord.fetchRequest()
            let lotteryCount = (try? context.count(for: lotteryRequest)) ?? 0
            if lotteryCount == 0 {
                missingData.append(.lotteryRecords)
            }
        }
        
        return missingData
    }
    
    /**
     * 从CloudKit恢复数据
     */
    private func restoreDataFromCloudKit(_ missingDataTypes: [MissingDataType]) async -> Bool {
        print("🔄 从CloudKit恢复数据: \(missingDataTypes)")
        
        var successCount = 0
        let totalCount = missingDataTypes.count
        
        for dataType in missingDataTypes {
            let success = await restoreSpecificDataType(dataType)
            if success {
                successCount += 1
            }
            
            // 更新进度
            restoreProgress = 0.4 + (Double(successCount) / Double(totalCount)) * 0.4
        }
        
        return successCount == totalCount
    }
    
    /**
     * 恢复特定类型的数据
     */
    private func restoreSpecificDataType(_ dataType: MissingDataType) async -> Bool {
        let database = cloudKitContainer.privateCloudDatabase
        
        do {
            let recordType = dataType.cloudKitRecordType
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))
            
            let (matchResults, _) = try await database.records(matching: query)
            let records = matchResults.compactMap { try? $0.1.get() }
            
            print("📦 从CloudKit获取到 \(records.count) 条 \(recordType) 记录")
            
            // 触发CloudKit同步，让Core Data自动处理记录
            await triggerCloudKitSync()
            
            return true
            
        } catch {
            print("❌ 恢复 \(dataType.cloudKitRecordType) 失败: \(error)")
            return false
        }
    }
    
    /**
     * 触发CloudKit同步
     */
    private func triggerCloudKitSync() async {
        // 保存上下文以触发CloudKit同步
        dataManager.persistenceController.save()
        
        // 等待同步完成
        try? await Task.sleep(nanoseconds: 2_000_000_000)
    }
    
    /**
     * 验证恢复的数据
     */
    private func verifyRestoredData() async -> Bool {
        // 等待一段时间让CloudKit同步完成
        try? await Task.sleep(nanoseconds: 3_000_000_000)
        
        let missingData = await identifyMissingData()
        return missingData.isEmpty
    }
    
    /**
     * 收集数据快照
     */
    private func collectDataSnapshot() async -> DataSnapshot {
        let context = dataManager.viewContext
        
        return await context.perform {
            var snapshot = DataSnapshot()
            snapshot.timestamp = Date()
            
            // 收集用户信息
            if let user = self.dataManager.currentUser {
                snapshot.userInfo = UserSnapshot(
                    id: user.id,
                    nickname: user.nickname,
                    email: user.email,
                    appleUserID: user.appleUserID,
                    subscriptionType: user.subscriptionType
                )
            }
            
            // 收集成员信息
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            if let members = try? context.fetch(memberRequest) {
                snapshot.members = members.map { member in
                    MemberSnapshot(
                        id: member.id,
                        name: member.name,
                        role: member.role,
                        currentPoints: Int(member.currentPoints),
                        memberNumber: Int(member.memberNumber)
                    )
                }
            }
            
            // 收集积分记录统计
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            if let pointRecords = try? context.fetch(pointRecordRequest) {
                snapshot.pointRecordCount = pointRecords.count
                snapshot.totalPointsEarned = pointRecords.filter { $0.value > 0 }.reduce(0) { $0 + Int($1.value) }
                snapshot.totalPointsSpent = pointRecords.filter { $0.value < 0 }.reduce(0) { $0 + abs(Int($1.value)) }
            }
            
            return snapshot
        }
    }
    
    /**
     * 上传快照到CloudKit
     */
    private func uploadSnapshotToCloudKit(_ snapshot: DataSnapshot) async -> Bool {
        // 这里可以实现将快照数据上传到CloudKit的自定义记录类型
        // 由于篇幅限制，这里只是模拟实现
        print("📤 上传数据快照到CloudKit...")
        
        // 模拟上传过程
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        return true
    }
}

// MARK: - Data Models

enum MissingDataType: CaseIterable {
    case pointRecords
    case members
    case diaryEntries
    case redemptionRecords
    case lotteryRecords
    
    var cloudKitRecordType: String {
        switch self {
        case .pointRecords: return "CD_PointRecord"
        case .members: return "CD_Member"
        case .diaryEntries: return "CD_DiaryEntry"
        case .redemptionRecords: return "CD_RedemptionRecord"
        case .lotteryRecords: return "CD_LotteryRecord"
        }
    }
}

enum RestoreStatus {
    case idle
    case checking
    case restoring
    case completed
    case failed
}

enum BackupError: LocalizedError {
    case cloudKitUnavailable
    case dataCorrupted
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .cloudKitUnavailable:
            return "iCloud不可用"
        case .dataCorrupted:
            return "数据损坏"
        case .networkError:
            return "网络错误"
        }
    }
}

struct DataSnapshot {
    var timestamp: Date = Date()
    var userInfo: UserSnapshot?
    var members: [MemberSnapshot] = []
    var pointRecordCount: Int = 0
    var totalPointsEarned: Int = 0
    var totalPointsSpent: Int = 0
}

struct UserSnapshot {
    let id: UUID?
    let nickname: String?
    let email: String?
    let appleUserID: String?
    let subscriptionType: String?
}

struct MemberSnapshot {
    let id: UUID?
    let name: String?
    let role: String?
    let currentPoints: Int
    let memberNumber: Int
}
