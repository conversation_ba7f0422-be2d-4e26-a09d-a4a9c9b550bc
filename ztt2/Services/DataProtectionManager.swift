//
//  DataProtectionManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import Combine

/**
 * 数据保护管理器
 * 实现多层数据保护机制，确保关键数据在应用卸载重装后能够恢复
 * 参考ztt1项目的TrialManager架构，实现三层存储：
 * 1. CoreData (最高优先级) - CloudKit自动同步
 * 2. NSUbiquitousKeyValueStore (中等优先级) - 跨设备设置同步
 * 3. UserDefaults (最低优先级) - 本地备份
 */
@MainActor
class DataProtectionManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataProtectionManager()
    
    // MARK: - Published Properties
    @Published var isInitialized: Bool = false
    @Published var lastBackupDate: Date?
    @Published var backupStatus: BackupStatus = .idle
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private let userDefaults = UserDefaults.standard
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Constants
    private struct BackupKeys {
        // 用户基本信息
        static let userNickname = "backup_user_nickname"
        static let userEmail = "backup_user_email"
        static let userAppleID = "backup_user_apple_id"
        static let userSubscriptionType = "backup_user_subscription_type"
        
        // 积分统计信息
        static let totalPointsEarned = "backup_total_points_earned"
        static let totalPointsSpent = "backup_total_points_spent"
        static let memberCount = "backup_member_count"
        
        // 记录统计
        static let pointRecordCount = "backup_point_record_count"
        static let diaryEntryCount = "backup_diary_entry_count"
        static let redemptionRecordCount = "backup_redemption_record_count"
        
        // 备份时间戳
        static let lastBackupTimestamp = "backup_last_timestamp"
        static let backupVersion = "backup_version"
    }
    
    private let currentBackupVersion = "1.0"
    
    // MARK: - Initialization
    private init() {
        setupObservers()
        loadBackupStatus()
    }
    
    // MARK: - Public Methods
    
    /**
     * 初始化数据保护
     */
    func initialize() async {
        print("🛡️ 初始化数据保护管理器...")
        
        // 检查是否需要数据恢复
        await checkAndRestoreData()
        
        // 创建初始备份
        await createBackup()
        
        isInitialized = true
        print("✅ 数据保护管理器初始化完成")
    }
    
    /**
     * 创建数据备份
     */
    func createBackup() async {
        backupStatus = .backing
        
        do {
            // 获取当前数据统计
            let stats = await collectDataStatistics()
            
            // 备份到NSUbiquitousKeyValueStore
            await backupToUbiquitousStore(stats)
            
            // 备份到UserDefaults
            backupToUserDefaults(stats)
            
            lastBackupDate = Date()
            backupStatus = .success
            errorMessage = nil
            
            print("✅ 数据备份完成")
            
        } catch {
            backupStatus = .failed
            errorMessage = error.localizedDescription
            print("❌ 数据备份失败: \(error)")
        }
    }
    
    /**
     * 检查并恢复数据
     */
    func checkAndRestoreData() async {
        print("🔍 检查数据完整性...")
        
        // 检查CoreData中是否有数据
        let hasLocalData = await checkLocalDataExists()
        
        if !hasLocalData {
            print("⚠️ 检测到本地数据缺失，尝试从备份恢复...")
            await attemptDataRestore()
        } else {
            print("✅ 本地数据完整")
        }
    }
    
    /**
     * 强制数据恢复
     */
    func forceDataRestore() async -> Bool {
        print("🔄 强制执行数据恢复...")
        return await attemptDataRestore()
    }
    
    /**
     * 验证数据一致性
     */
    func validateDataConsistency() async -> DataConsistencyReport {
        let localStats = await collectDataStatistics()
        let cloudStats = loadStatsFromUbiquitousStore()
        let userDefaultsStats = loadStatsFromUserDefaults()
        
        return DataConsistencyReport(
            localStats: localStats,
            cloudStats: cloudStats,
            userDefaultsStats: userDefaultsStats,
            isConsistent: compareStats(localStats, cloudStats, userDefaultsStats)
        )
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听NSUbiquitousKeyValueStore变化
        NotificationCenter.default.publisher(for: NSUbiquitousKeyValueStore.didChangeExternallyNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleExternalStoreChange()
                }
            }
            .store(in: &cancellables)
        
        // 监听应用进入后台，创建备份
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.createBackup()
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 加载备份状态
     */
    private func loadBackupStatus() {
        if let timestamp = userDefaults.object(forKey: BackupKeys.lastBackupTimestamp) as? Date {
            lastBackupDate = timestamp
        }
    }
    
    /**
     * 收集数据统计信息
     */
    private func collectDataStatistics() async -> DataStatistics {
        let context = dataManager.viewContext

        return await context.perform {
            var stats = DataStatistics()

            // 用户信息
            if let user = self.dataManager.currentUser {
                stats.userNickname = user.nickname
                stats.userEmail = user.email
                stats.userAppleID = user.appleUserID
                stats.userSubscriptionType = user.subscriptionType
            }

            // 成员统计
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            stats.memberCount = (try? context.count(for: memberRequest)) ?? 0

            // 积分记录统计
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            stats.pointRecordCount = (try? context.count(for: pointRecordRequest)) ?? 0

            // 计算总积分
            let allPointRecords = (try? context.fetch(pointRecordRequest)) ?? []
            stats.totalPointsEarned = allPointRecords.filter { $0.value > 0 }.reduce(0) { $0 + Int($1.value) }
            stats.totalPointsSpent = allPointRecords.filter { $0.value < 0 }.reduce(0) { $0 + abs(Int($1.value)) }

            // 日记统计
            let diaryRequest: NSFetchRequest<DiaryEntry> = DiaryEntry.fetchRequest()
            stats.diaryEntryCount = (try? context.count(for: diaryRequest)) ?? 0

            // 兑换记录统计
            let redemptionRequest: NSFetchRequest<RedemptionRecord> = RedemptionRecord.fetchRequest()
            stats.redemptionRecordCount = (try? context.count(for: redemptionRequest)) ?? 0

            return stats
        }
    }

    /**
     * 备份到NSUbiquitousKeyValueStore
     */
    private func backupToUbiquitousStore(_ stats: DataStatistics) async {
        ubiquitousStore.set(stats.userNickname ?? "", forKey: BackupKeys.userNickname)
        ubiquitousStore.set(stats.userEmail ?? "", forKey: BackupKeys.userEmail)
        ubiquitousStore.set(stats.userAppleID ?? "", forKey: BackupKeys.userAppleID)
        ubiquitousStore.set(stats.userSubscriptionType ?? "free", forKey: BackupKeys.userSubscriptionType)

        ubiquitousStore.set(stats.totalPointsEarned, forKey: BackupKeys.totalPointsEarned)
        ubiquitousStore.set(stats.totalPointsSpent, forKey: BackupKeys.totalPointsSpent)
        ubiquitousStore.set(stats.memberCount, forKey: BackupKeys.memberCount)

        ubiquitousStore.set(stats.pointRecordCount, forKey: BackupKeys.pointRecordCount)
        ubiquitousStore.set(stats.diaryEntryCount, forKey: BackupKeys.diaryEntryCount)
        ubiquitousStore.set(stats.redemptionRecordCount, forKey: BackupKeys.redemptionRecordCount)

        ubiquitousStore.set(Date(), forKey: BackupKeys.lastBackupTimestamp)
        ubiquitousStore.set(currentBackupVersion, forKey: BackupKeys.backupVersion)

        ubiquitousStore.synchronize()
        print("☁️ 数据已备份到NSUbiquitousKeyValueStore")
    }

    /**
     * 备份到UserDefaults
     */
    private func backupToUserDefaults(_ stats: DataStatistics) {
        userDefaults.set(stats.userNickname ?? "", forKey: BackupKeys.userNickname)
        userDefaults.set(stats.userEmail ?? "", forKey: BackupKeys.userEmail)
        userDefaults.set(stats.userAppleID ?? "", forKey: BackupKeys.userAppleID)
        userDefaults.set(stats.userSubscriptionType ?? "free", forKey: BackupKeys.userSubscriptionType)

        userDefaults.set(stats.totalPointsEarned, forKey: BackupKeys.totalPointsEarned)
        userDefaults.set(stats.totalPointsSpent, forKey: BackupKeys.totalPointsSpent)
        userDefaults.set(stats.memberCount, forKey: BackupKeys.memberCount)

        userDefaults.set(stats.pointRecordCount, forKey: BackupKeys.pointRecordCount)
        userDefaults.set(stats.diaryEntryCount, forKey: BackupKeys.diaryEntryCount)
        userDefaults.set(stats.redemptionRecordCount, forKey: BackupKeys.redemptionRecordCount)

        userDefaults.set(Date(), forKey: BackupKeys.lastBackupTimestamp)
        userDefaults.set(currentBackupVersion, forKey: BackupKeys.backupVersion)

        userDefaults.synchronize()
        print("💾 数据已备份到UserDefaults")
    }

    /**
     * 检查本地数据是否存在
     */
    private func checkLocalDataExists() async -> Bool {
        let context = dataManager.viewContext

        return await context.perform {
            // 检查是否有用户数据
            let userRequest: NSFetchRequest<User> = User.fetchRequest()
            let userCount = (try? context.count(for: userRequest)) ?? 0

            // 检查是否有成员数据
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            let memberCount = (try? context.count(for: memberRequest)) ?? 0

            // 检查是否有积分记录
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            let pointRecordCount = (try? context.count(for: pointRecordRequest)) ?? 0

            return userCount > 0 || memberCount > 0 || pointRecordCount > 0
        }
    }

    /**
     * 尝试数据恢复
     */
    @discardableResult
    private func attemptDataRestore() async -> Bool {
        print("🔄 开始数据恢复流程...")

        // 首先尝试从NSUbiquitousKeyValueStore恢复
        if await restoreFromUbiquitousStore() {
            print("✅ 从NSUbiquitousKeyValueStore恢复成功")
            return true
        }

        // 然后尝试从UserDefaults恢复
        if restoreFromUserDefaults() {
            print("✅ 从UserDefaults恢复成功")
            return true
        }

        print("❌ 数据恢复失败，没有找到可用的备份")
        return false
    }

    /**
     * 从NSUbiquitousKeyValueStore恢复数据
     */
    private func restoreFromUbiquitousStore() async -> Bool {
        // 强制同步云端数据
        ubiquitousStore.synchronize()

        // 等待同步完成
        try? await Task.sleep(nanoseconds: 1_000_000_000)

        let stats = loadStatsFromUbiquitousStore()
        return stats != nil && await restoreDataFromStats(stats!)
    }

    /**
     * 从UserDefaults恢复数据
     */
    private func restoreFromUserDefaults() -> Bool {
        guard let stats = loadStatsFromUserDefaults() else {
            return false
        }

        // 由于UserDefaults只有统计信息，这里主要是验证数据存在
        // 实际的数据恢复需要等待CloudKit同步
        print("📱 从UserDefaults检测到备份数据，等待CloudKit同步...")
        return true
    }
}

// MARK: - Data Models

struct DataStatistics {
    var userNickname: String?
    var userEmail: String?
    var userAppleID: String?
    var userSubscriptionType: String?
    var memberCount: Int = 0
    var pointRecordCount: Int = 0
    var totalPointsEarned: Int = 0
    var totalPointsSpent: Int = 0
    var diaryEntryCount: Int = 0
    var redemptionRecordCount: Int = 0
}

struct DataConsistencyReport {
    let localStats: DataStatistics
    let cloudStats: DataStatistics?
    let userDefaultsStats: DataStatistics?
    let isConsistent: Bool
}

enum BackupStatus {
    case idle
    case backing
    case success
    case failed
}

// MARK: - DataProtectionManager Extension

extension DataProtectionManager {

    /**
     * 从NSUbiquitousKeyValueStore加载统计信息
     */
    private func loadStatsFromUbiquitousStore() -> DataStatistics? {
        let version = ubiquitousStore.string(forKey: BackupKeys.backupVersion)
        guard version == currentBackupVersion else {
            print("⚠️ NSUbiquitousKeyValueStore备份版本不匹配")
            return nil
        }

        var stats = DataStatistics()
        stats.userNickname = ubiquitousStore.string(forKey: BackupKeys.userNickname)
        stats.userEmail = ubiquitousStore.string(forKey: BackupKeys.userEmail)
        stats.userAppleID = ubiquitousStore.string(forKey: BackupKeys.userAppleID)
        stats.userSubscriptionType = ubiquitousStore.string(forKey: BackupKeys.userSubscriptionType)

        stats.totalPointsEarned = Int(ubiquitousStore.longLong(forKey: BackupKeys.totalPointsEarned))
        stats.totalPointsSpent = Int(ubiquitousStore.longLong(forKey: BackupKeys.totalPointsSpent))
        stats.memberCount = Int(ubiquitousStore.longLong(forKey: BackupKeys.memberCount))

        stats.pointRecordCount = Int(ubiquitousStore.longLong(forKey: BackupKeys.pointRecordCount))
        stats.diaryEntryCount = Int(ubiquitousStore.longLong(forKey: BackupKeys.diaryEntryCount))
        stats.redemptionRecordCount = Int(ubiquitousStore.longLong(forKey: BackupKeys.redemptionRecordCount))

        return stats
    }

    /**
     * 从UserDefaults加载统计信息
     */
    private func loadStatsFromUserDefaults() -> DataStatistics? {
        let version = userDefaults.string(forKey: BackupKeys.backupVersion)
        guard version == currentBackupVersion else {
            print("⚠️ UserDefaults备份版本不匹配")
            return nil
        }

        var stats = DataStatistics()
        stats.userNickname = userDefaults.string(forKey: BackupKeys.userNickname)
        stats.userEmail = userDefaults.string(forKey: BackupKeys.userEmail)
        stats.userAppleID = userDefaults.string(forKey: BackupKeys.userAppleID)
        stats.userSubscriptionType = userDefaults.string(forKey: BackupKeys.userSubscriptionType)

        stats.totalPointsEarned = userDefaults.integer(forKey: BackupKeys.totalPointsEarned)
        stats.totalPointsSpent = userDefaults.integer(forKey: BackupKeys.totalPointsSpent)
        stats.memberCount = userDefaults.integer(forKey: BackupKeys.memberCount)

        stats.pointRecordCount = userDefaults.integer(forKey: BackupKeys.pointRecordCount)
        stats.diaryEntryCount = userDefaults.integer(forKey: BackupKeys.diaryEntryCount)
        stats.redemptionRecordCount = userDefaults.integer(forKey: BackupKeys.redemptionRecordCount)

        return stats
    }

    /**
     * 从统计信息恢复数据
     */
    private func restoreDataFromStats(_ stats: DataStatistics) async -> Bool {
        // 这里主要是创建用户基本信息，实际的详细数据需要等待CloudKit同步
        let context = dataManager.viewContext

        return await context.perform {
            // 检查是否已有用户
            let userRequest: NSFetchRequest<User> = User.fetchRequest()
            if let existingUser = try? context.fetch(userRequest).first {
                print("✅ 用户已存在，跳过恢复")
                return true
            }

            // 创建用户
            let user = User(context: context)
            user.id = UUID()
            user.nickname = stats.userNickname
            user.email = stats.userEmail
            user.appleUserID = stats.userAppleID
            user.subscriptionType = stats.userSubscriptionType ?? "free"
            user.createdAt = Date()

            // 创建订阅信息
            let subscription = Subscription(context: context)
            subscription.id = UUID()
            subscription.subscriptionType = stats.userSubscriptionType ?? "free"
            subscription.level = stats.userSubscriptionType ?? "free"
            subscription.isActive = true
            subscription.createdAt = Date()
            subscription.updatedAt = Date()
            subscription.user = user

            do {
                try context.save()
                print("✅ 基本用户信息恢复成功")
                return true
            } catch {
                print("❌ 用户信息恢复失败: \(error)")
                return false
            }
        }
    }

    /**
     * 比较统计信息
     */
    private func compareStats(_ local: DataStatistics, _ cloud: DataStatistics?, _ userDefaults: DataStatistics?) -> Bool {
        guard let cloud = cloud, let userDefaults = userDefaults else {
            return false
        }

        // 检查关键数据是否一致
        let memberCountConsistent = (local.memberCount == cloud.memberCount) && (cloud.memberCount == userDefaults.memberCount)
        let pointRecordCountConsistent = (local.pointRecordCount == cloud.pointRecordCount) && (cloud.pointRecordCount == userDefaults.pointRecordCount)
        let totalPointsConsistent = (local.totalPointsEarned == cloud.totalPointsEarned) && (cloud.totalPointsEarned == userDefaults.totalPointsEarned)

        return memberCountConsistent && pointRecordCountConsistent && totalPointsConsistent
    }

    /**
     * 处理外部存储变化
     */
    private func handleExternalStoreChange() {
        print("🔄 检测到NSUbiquitousKeyValueStore外部变化")

        Task {
            // 检查数据一致性
            let report = await validateDataConsistency()
            if !report.isConsistent {
                print("⚠️ 检测到数据不一致，尝试同步...")
                await createBackup()
            }
        }
    }
}

// MARK: - Debug Helper

#if DEBUG
extension DataProtectionManager {

    /**
     * 打印所有备份状态（调试用）
     */
    func printAllBackupStates() {
        print("🔍 === 数据保护状态调试信息 ===")

        // 本地数据
        Task {
            let localStats = await collectDataStatistics()
            print("💾 本地数据:")
            print("   成员数量: \(localStats.memberCount)")
            print("   积分记录: \(localStats.pointRecordCount)")
            print("   总获得积分: \(localStats.totalPointsEarned)")
            print("   总消费积分: \(localStats.totalPointsSpent)")
        }

        // NSUbiquitousKeyValueStore
        if let cloudStats = loadStatsFromUbiquitousStore() {
            print("☁️ NSUbiquitousKeyValueStore:")
            print("   成员数量: \(cloudStats.memberCount)")
            print("   积分记录: \(cloudStats.pointRecordCount)")
            print("   总获得积分: \(cloudStats.totalPointsEarned)")
            print("   总消费积分: \(cloudStats.totalPointsSpent)")
        } else {
            print("☁️ NSUbiquitousKeyValueStore: 无备份数据")
        }

        // UserDefaults
        if let userDefaultsStats = loadStatsFromUserDefaults() {
            print("📱 UserDefaults:")
            print("   成员数量: \(userDefaultsStats.memberCount)")
            print("   积分记录: \(userDefaultsStats.pointRecordCount)")
            print("   总获得积分: \(userDefaultsStats.totalPointsEarned)")
            print("   总消费积分: \(userDefaultsStats.totalPointsSpent)")
        } else {
            print("📱 UserDefaults: 无备份数据")
        }

        print("🔍 === 调试信息结束 ===")
    }

    /**
     * 清除所有备份数据（调试用）
     */
    func clearAllBackups() {
        print("🧹 清除所有备份数据...")

        // 清除NSUbiquitousKeyValueStore
        for key in [BackupKeys.userNickname, BackupKeys.userEmail, BackupKeys.userAppleID,
                   BackupKeys.userSubscriptionType, BackupKeys.totalPointsEarned, BackupKeys.totalPointsSpent,
                   BackupKeys.memberCount, BackupKeys.pointRecordCount, BackupKeys.diaryEntryCount,
                   BackupKeys.redemptionRecordCount, BackupKeys.lastBackupTimestamp, BackupKeys.backupVersion] {
            ubiquitousStore.removeObject(forKey: key)
        }
        ubiquitousStore.synchronize()

        // 清除UserDefaults
        for key in [BackupKeys.userNickname, BackupKeys.userEmail, BackupKeys.userAppleID,
                   BackupKeys.userSubscriptionType, BackupKeys.totalPointsEarned, BackupKeys.totalPointsSpent,
                   BackupKeys.memberCount, BackupKeys.pointRecordCount, BackupKeys.diaryEntryCount,
                   BackupKeys.redemptionRecordCount, BackupKeys.lastBackupTimestamp, BackupKeys.backupVersion] {
            userDefaults.removeObject(forKey: key)
        }
        userDefaults.synchronize()

        lastBackupDate = nil
        print("✅ 所有备份数据已清除")
    }
}
#endif
