//
//  DataConsistencyValidator.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit
import Combine

/**
 * 数据一致性验证工具
 * 用于诊断和修复多设备间的数据同步问题
 * 参考ztt1项目的TrialDebugHelper，提供全面的数据一致性检查
 */
@MainActor
class DataConsistencyValidator: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataConsistencyValidator()
    
    // MARK: - Published Properties
    @Published var isValidating: Bool = false
    @Published var validationProgress: Double = 0.0
    @Published var lastValidationDate: Date?
    @Published var validationResults: [ValidationResult] = []
    
    // MARK: - Private Properties
    private let dataManager = DataManager.shared
    private let dataProtectionManager = DataProtectionManager.shared
    private let cloudKitContainer = CKContainer.default()
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 执行完整的数据一致性验证
     */
    func performFullValidation() async {
        print("🔍 开始完整数据一致性验证...")
        
        isValidating = true
        validationProgress = 0.0
        validationResults.removeAll()
        
        // 1. 验证本地数据完整性
        await validateLocalDataIntegrity()
        validationProgress = 0.2
        
        // 2. 验证CloudKit连接
        await validateCloudKitConnection()
        validationProgress = 0.4
        
        // 3. 验证数据同步状态
        await validateSyncStatus()
        validationProgress = 0.6
        
        // 4. 验证多层备份一致性
        await validateBackupConsistency()
        validationProgress = 0.8
        
        // 5. 生成修复建议
        generateRepairSuggestions()
        validationProgress = 1.0
        
        lastValidationDate = Date()
        isValidating = false
        
        print("✅ 数据一致性验证完成，发现 \(validationResults.count) 个问题")
    }
    
    /**
     * 快速验证关键数据
     */
    func performQuickValidation() async -> QuickValidationResult {
        print("⚡ 执行快速数据验证...")
        
        let context = dataManager.viewContext
        
        return await context.perform {
            var result = QuickValidationResult()
            
            // 检查用户数据
            let userRequest: NSFetchRequest<User> = User.fetchRequest()
            result.userCount = (try? context.count(for: userRequest)) ?? 0
            
            // 检查成员数据
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            result.memberCount = (try? context.count(for: memberRequest)) ?? 0
            
            // 检查积分记录
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            result.pointRecordCount = (try? context.count(for: pointRecordRequest)) ?? 0
            
            // 检查孤立记录
            pointRecordRequest.predicate = NSPredicate(format: "member == nil")
            result.orphanedRecordCount = (try? context.count(for: pointRecordRequest)) ?? 0
            
            // 计算总积分
            pointRecordRequest.predicate = nil
            if let allRecords = try? context.fetch(pointRecordRequest) {
                result.totalPoints = allRecords.reduce(0) { $0 + Int($1.value) }
            }
            
            result.isHealthy = result.userCount > 0 && result.orphanedRecordCount == 0
            
            return result
        }
    }
    
    /**
     * 修复检测到的问题
     */
    func repairDetectedIssues() async -> RepairResult {
        print("🔧 开始修复检测到的问题...")
        
        var repairedCount = 0
        var failedCount = 0
        
        for result in validationResults {
            switch result.severity {
            case .critical, .high:
                let success = await repairIssue(result)
                if success {
                    repairedCount += 1
                } else {
                    failedCount += 1
                }
            case .medium, .low:
                // 中低优先级问题暂时跳过
                break
            }
        }
        
        // 重新验证修复结果
        let _ = await performQuickValidation()
        
        return RepairResult(
            repairedCount: repairedCount,
            failedCount: failedCount,
            totalIssues: validationResults.count
        )
    }
    
    /**
     * 打印详细的验证报告
     */
    func printDetailedReport() {
        print("📊 === 数据一致性验证报告 ===")
        print("验证时间: \(lastValidationDate?.description ?? "未验证")")
        print("发现问题: \(validationResults.count) 个")
        print("")
        
        let groupedResults = Dictionary(grouping: validationResults) { $0.category }
        
        for (category, results) in groupedResults {
            print("📂 \(category.displayName):")
            for result in results {
                let severityIcon = result.severity.icon
                print("   \(severityIcon) \(result.title)")
                print("      \(result.description)")
                if let suggestion = result.suggestion {
                    print("      💡 建议: \(suggestion)")
                }
                print("")
            }
        }
        
        print("📊 === 报告结束 ===")
    }
    
    // MARK: - Private Methods
    
    /**
     * 验证本地数据完整性
     */
    private func validateLocalDataIntegrity() async {
        let context = dataManager.viewContext
        
        await context.perform {
            // 检查用户数据
            let userRequest: NSFetchRequest<User> = User.fetchRequest()
            let userCount = (try? context.count(for: userRequest)) ?? 0
            
            if userCount == 0 {
                self.validationResults.append(ValidationResult(
                    category: .dataIntegrity,
                    severity: .critical,
                    title: "缺少用户数据",
                    description: "系统中没有找到用户数据，这可能导致应用无法正常工作",
                    suggestion: "尝试重新登录或从备份恢复数据"
                ))
            } else if userCount > 1 {
                self.validationResults.append(ValidationResult(
                    category: .dataIntegrity,
                    severity: .high,
                    title: "存在多个用户记录",
                    description: "检测到 \(userCount) 个用户记录，应该只有一个",
                    suggestion: "清理重复的用户记录"
                ))
            }
            
            // 检查孤立的积分记录
            let pointRecordRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            pointRecordRequest.predicate = NSPredicate(format: "member == nil")
            let orphanedCount = (try? context.count(for: pointRecordRequest)) ?? 0
            
            if orphanedCount > 0 {
                self.validationResults.append(ValidationResult(
                    category: .dataIntegrity,
                    severity: .medium,
                    title: "存在孤立的积分记录",
                    description: "发现 \(orphanedCount) 条没有关联成员的积分记录",
                    suggestion: "清理孤立的积分记录"
                ))
            }
            
            // 检查成员积分一致性
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            if let members = try? context.fetch(memberRequest) {
                for member in members {
                    let calculatedPoints = member.pointRecords?.allObjects.compactMap { $0 as? PointRecord }
                        .reduce(0) { $0 + Int($1.value) } ?? 0
                    
                    if calculatedPoints != Int(member.currentPoints) {
                        self.validationResults.append(ValidationResult(
                            category: .dataIntegrity,
                            severity: .high,
                            title: "成员积分不一致",
                            description: "\(member.name ?? "未知成员") 的积分不一致：计算值 \(calculatedPoints)，存储值 \(member.currentPoints)",
                            suggestion: "重新计算并更新成员积分"
                        ))
                    }
                }
            }
        }
    }
    
    /**
     * 验证CloudKit连接
     */
    private func validateCloudKitConnection() async {
        do {
            let status = try await cloudKitContainer.accountStatus()
            
            switch status {
            case .available:
                print("✅ CloudKit连接正常")
            case .noAccount:
                validationResults.append(ValidationResult(
                    category: .cloudKit,
                    severity: .critical,
                    title: "未登录iCloud账户",
                    description: "设备未登录iCloud账户，无法进行数据同步",
                    suggestion: "在设置中登录iCloud账户"
                ))
            case .restricted:
                validationResults.append(ValidationResult(
                    category: .cloudKit,
                    severity: .high,
                    title: "iCloud访问受限",
                    description: "iCloud访问受到限制，可能是家长控制或企业策略",
                    suggestion: "检查设备的iCloud访问权限设置"
                ))
            case .couldNotDetermine:
                validationResults.append(ValidationResult(
                    category: .cloudKit,
                    severity: .medium,
                    title: "无法确定iCloud状态",
                    description: "无法确定iCloud账户状态，可能是网络问题",
                    suggestion: "检查网络连接并重试"
                ))
            @unknown default:
                validationResults.append(ValidationResult(
                    category: .cloudKit,
                    severity: .medium,
                    title: "未知的iCloud状态",
                    description: "检测到未知的iCloud账户状态",
                    suggestion: "重启应用或联系技术支持"
                ))
            }
        } catch {
            validationResults.append(ValidationResult(
                category: .cloudKit,
                severity: .high,
                title: "CloudKit连接失败",
                description: "无法连接到CloudKit服务：\(error.localizedDescription)",
                suggestion: "检查网络连接和iCloud设置"
            ))
        }
    }
    
    /**
     * 验证同步状态
     */
    private func validateSyncStatus() async {
        let coreDataManager = CoreDataManager.shared
        let summary = coreDataManager.getSyncStatusSummary()
        
        switch summary.status {
        case .syncFailed:
            validationResults.append(ValidationResult(
                category: .sync,
                severity: .high,
                title: "同步失败",
                description: summary.errorMessage ?? "CloudKit同步失败",
                suggestion: "尝试手动重试同步或检查网络连接"
            ))
        case .notStarted:
            validationResults.append(ValidationResult(
                category: .sync,
                severity: .medium,
                title: "同步未开始",
                description: "CloudKit同步尚未开始",
                suggestion: "触发一次数据保存以启动同步"
            ))
        default:
            break
        }
        
        // 检查重试次数
        if summary.retryCount > 0 {
            validationResults.append(ValidationResult(
                category: .sync,
                severity: .medium,
                title: "同步重试中",
                description: "CloudKit同步已重试 \(summary.retryCount) 次",
                suggestion: "监控同步状态，如果持续失败请检查网络"
            ))
        }
        
        // 检查数据完整性状态
        switch summary.dataIntegrity {
        case .compromised:
            validationResults.append(ValidationResult(
                category: .sync,
                severity: .critical,
                title: "数据完整性受损",
                description: "检测到数据完整性问题",
                suggestion: "立即执行数据修复或从备份恢复"
            ))
        case .unknown:
            validationResults.append(ValidationResult(
                category: .sync,
                severity: .low,
                title: "数据完整性未知",
                description: "尚未检查数据完整性",
                suggestion: "执行数据完整性检查"
            ))
        default:
            break
        }
    }
    
    /**
     * 验证备份一致性
     */
    private func validateBackupConsistency() async {
        let report = await dataProtectionManager.validateDataConsistency()
        
        if !report.isConsistent {
            validationResults.append(ValidationResult(
                category: .backup,
                severity: .medium,
                title: "备份数据不一致",
                description: "多层备份数据之间存在不一致",
                suggestion: "重新创建备份或同步备份数据"
            ))
        }
        
        // 检查备份时间
        if let lastBackup = dataProtectionManager.lastBackupDate {
            let timeSinceBackup = Date().timeIntervalSince(lastBackup)
            if timeSinceBackup > 24 * 60 * 60 { // 超过24小时
                validationResults.append(ValidationResult(
                    category: .backup,
                    severity: .low,
                    title: "备份时间过久",
                    description: "上次备份时间超过24小时",
                    suggestion: "创建新的数据备份"
                ))
            }
        } else {
            validationResults.append(ValidationResult(
                category: .backup,
                severity: .medium,
                title: "缺少备份记录",
                description: "没有找到数据备份记录",
                suggestion: "立即创建数据备份"
            ))
        }
    }
    
    /**
     * 生成修复建议
     */
    private func generateRepairSuggestions() {
        let criticalIssues = validationResults.filter { $0.severity == .critical }
        let highIssues = validationResults.filter { $0.severity == .high }
        
        if !criticalIssues.isEmpty {
            validationResults.append(ValidationResult(
                category: .general,
                severity: .critical,
                title: "需要立即处理",
                description: "发现 \(criticalIssues.count) 个严重问题需要立即处理",
                suggestion: "按优先级逐一修复问题，必要时联系技术支持"
            ))
        }
        
        if !highIssues.isEmpty {
            validationResults.append(ValidationResult(
                category: .general,
                severity: .high,
                title: "建议尽快处理",
                description: "发现 \(highIssues.count) 个高优先级问题",
                suggestion: "在方便时修复这些问题以确保数据安全"
            ))
        }
    }
    
    /**
     * 修复单个问题
     */
    private func repairIssue(_ result: ValidationResult) async -> Bool {
        print("🔧 修复问题: \(result.title)")
        
        switch result.category {
        case .dataIntegrity:
            return await repairDataIntegrityIssue(result)
        case .cloudKit:
            return await repairCloudKitIssue(result)
        case .sync:
            return await repairSyncIssue(result)
        case .backup:
            return await repairBackupIssue(result)
        case .general:
            return false // 一般性问题不能自动修复
        @unknown default:
            return false
        }
    }
    
    /**
     * 修复数据完整性问题
     */
    private func repairDataIntegrityIssue(_ result: ValidationResult) async -> Bool {
        if result.title.contains("孤立的积分记录") {
            await CoreDataManager.shared.checkDataIntegrity()
            return true
        }
        
        if result.title.contains("积分不一致") {
            await CoreDataManager.shared.checkDataIntegrity()
            return true
        }
        
        return false
    }
    
    /**
     * 修复CloudKit问题
     */
    private func repairCloudKitIssue(_ result: ValidationResult) async -> Bool {
        // CloudKit问题通常需要用户手动处理
        return false
    }
    
    /**
     * 修复同步问题
     */
    private func repairSyncIssue(_ result: ValidationResult) async -> Bool {
        if result.title.contains("同步失败") {
            await CoreDataManager.shared.forceSyncRetry()
            return true
        }
        
        return false
    }
    
    /**
     * 修复备份问题
     */
    private func repairBackupIssue(_ result: ValidationResult) async -> Bool {
        if result.title.contains("备份") {
            await dataProtectionManager.createBackup()
            return true
        }
        
        return false
    }
}

// MARK: - Data Models

struct ValidationResult {
    let category: ValidationCategory
    let severity: ValidationSeverity
    let title: String
    let description: String
    let suggestion: String?

    init(category: ValidationCategory, severity: ValidationSeverity, title: String, description: String, suggestion: String? = nil) {
        self.category = category
        self.severity = severity
        self.title = title
        self.description = description
        self.suggestion = suggestion
    }
}

enum ValidationCategory {
    case dataIntegrity
    case cloudKit
    case sync
    case backup
    case general

    var displayName: String {
        switch self {
        case .dataIntegrity: return "数据完整性"
        case .cloudKit: return "CloudKit"
        case .sync: return "数据同步"
        case .backup: return "数据备份"
        case .general: return "综合"
        }
    }
}

enum ValidationSeverity {
    case critical
    case high
    case medium
    case low

    var icon: String {
        switch self {
        case .critical: return "🔴"
        case .high: return "🟠"
        case .medium: return "🟡"
        case .low: return "🟢"
        }
    }

    var displayName: String {
        switch self {
        case .critical: return "严重"
        case .high: return "高"
        case .medium: return "中"
        case .low: return "低"
        }
    }
}

struct QuickValidationResult {
    var userCount: Int = 0
    var memberCount: Int = 0
    var pointRecordCount: Int = 0
    var orphanedRecordCount: Int = 0
    var totalPoints: Int = 0
    var isHealthy: Bool = false

    var summary: String {
        return """
        用户: \(userCount), 成员: \(memberCount), 积分记录: \(pointRecordCount)
        孤立记录: \(orphanedRecordCount), 总积分: \(totalPoints)
        状态: \(isHealthy ? "健康" : "异常")
        """
    }
}

struct RepairResult {
    let repairedCount: Int
    let failedCount: Int
    let totalIssues: Int

    var successRate: Double {
        guard totalIssues > 0 else { return 1.0 }
        return Double(repairedCount) / Double(totalIssues)
    }

    var summary: String {
        return "修复成功: \(repairedCount), 修复失败: \(failedCount), 总问题: \(totalIssues)"
    }
}

// MARK: - Debug Helper

#if DEBUG
extension DataConsistencyValidator {

    /**
     * 模拟数据问题（仅用于测试）
     */
    func simulateDataIssues() async {
        print("🧪 模拟数据问题用于测试...")

        let context = dataManager.viewContext

        await context.perform {
            // 创建孤立的积分记录
            let orphanedRecord = PointRecord(context: context)
            orphanedRecord.id = UUID()
            orphanedRecord.reason = "测试孤立记录"
            orphanedRecord.value = 10
            orphanedRecord.timestamp = Date()
            // 故意不设置member关联

            try? context.save()
        }

        print("✅ 数据问题模拟完成")
    }

    /**
     * 清理测试数据
     */
    func cleanupTestData() async {
        print("🧹 清理测试数据...")

        let context = dataManager.viewContext

        await context.perform {
            // 删除所有孤立的积分记录
            let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            request.predicate = NSPredicate(format: "member == nil")

            if let orphanedRecords = try? context.fetch(request) {
                for record in orphanedRecords {
                    context.delete(record)
                }

                try? context.save()
                print("✅ 清理了 \(orphanedRecords.count) 条孤立记录")
            }
        }
    }
}
#endif
